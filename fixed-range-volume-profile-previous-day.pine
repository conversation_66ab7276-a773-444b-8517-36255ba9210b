//@version=5
indicator("Fixed Range Volume Profile - Previous Day", "FRVP-PD", overlay=true, max_boxes_count=500)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// INPUTS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Profile Settings
show_profile = input.bool(true, "Show Volume Profile", tooltip="Toggle the display of the volume profile visualization", group="Profile Settings")
show_bounds = input.bool(true, "Show Bounding Box", tooltip="Toggle the display of the bounding box around the volume profile", group="Profile Settings")
profile_resolution = input.int(50, "Profile Resolution", minval=10, maxval=100, tooltip="Number of price levels in the volume profile. Higher values create more detailed profiles", group="Profile Settings")
profile_width = input.int(50, "Profile Width", minval=10, maxval=100, tooltip="Width of the volume profile bars", group="Profile Settings")
show_poc = input.bool(true, "Show Point of Control", tooltip="Highlight the price level with highest volume", group="Profile Settings")

// Appearance Settings
hvn_color = input.color(color.new(color.red, 30), "High Volume Nodes", tooltip="Color for high volume price levels", group="Appearance")
mvn_color = input.color(color.new(color.yellow, 50), "Medium Volume Nodes", tooltip="Color for medium volume price levels", group="Appearance")
lvn_color = input.color(color.new(color.blue, 70), "Low Volume Nodes", tooltip="Color for low volume price levels", group="Appearance")
poc_color = input.color(color.new(color.white, 0), "Point of Control", tooltip="Color for the Point of Control level", group="Appearance")
bounds_color = input.color(color.new(color.gray, 80), "Bounding Box", tooltip="Color for the profile boundary box", group="Appearance")

// Profile Position
profile_offset = input.int(10, "Profile Offset", minval=0, maxval=100, tooltip="Horizontal offset of the profile from current bar", group="Position")

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// FUNCTIONS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Function to get previous day's high and low
get_prev_day_levels() =>
    prev_high = request.security(syminfo.tickerid, "D", high[1], barmerge.gaps_off, barmerge.lookahead_off)
    prev_low = request.security(syminfo.tickerid, "D", low[1], barmerge.gaps_off, barmerge.lookahead_off)
    [prev_high, prev_low]

// Function to calculate volume profile
calculate_volume_profile(start_bar, end_bar, top_price, bottom_price, resolution) =>
    // Initialize arrays for volume storage
    volume_bins = array.new<float>(resolution, 0.0)
    price_step = (top_price - bottom_price) / resolution
    
    // Collect volume data for the range
    if not na(start_bar) and not na(end_bar) and start_bar <= end_bar
        for i = 0 to math.min(end_bar - start_bar, 5000) // Limit lookback to prevent timeout
            bar_idx = end_bar - i
            if bar_idx >= 0
                bar_high = high[bar_idx]
                bar_low = low[bar_idx]
                bar_volume = volume[bar_idx]
                
                if not na(bar_volume) and bar_volume > 0
                    // Determine which price bins this bar's range covers
                    high_bin = math.floor((bar_high - bottom_price) / price_step)
                    low_bin = math.floor((bar_low - bottom_price) / price_step)
                    
                    // Ensure bins are within valid range
                    high_bin := math.max(0, math.min(resolution - 1, high_bin))
                    low_bin := math.max(0, math.min(resolution - 1, low_bin))
                    
                    // Distribute volume across the bins the bar covers
                    bins_covered = math.max(1, high_bin - low_bin + 1)
                    volume_per_bin = bar_volume / bins_covered
                    
                    for bin_idx = low_bin to high_bin
                        current_volume = array.get(volume_bins, bin_idx)
                        array.set(volume_bins, bin_idx, current_volume + volume_per_bin)
    
    volume_bins

// Function to find Point of Control (highest volume level)
find_poc(volume_bins) =>
    max_volume = 0.0
    poc_level = 0
    
    for i = 0 to array.size(volume_bins) - 1
        vol = array.get(volume_bins, i)
        if vol > max_volume
            max_volume := vol
            poc_level := i
    
    [poc_level, max_volume]

// Function to draw volume profile
draw_volume_profile(volume_bins, top_price, bottom_price, resolution, max_volume, poc_level) =>
    price_step = (top_price - bottom_price) / resolution
    profile_start_bar = bar_index + profile_offset
    
    // Clear previous profile boxes
    var profile_boxes = array.new<box>()
    var boundary_box = box(na)
    
    // Delete existing boxes
    for i = 0 to array.size(profile_boxes) - 1
        box.delete(array.get(profile_boxes, i))
    array.clear(profile_boxes)
    
    if not na(boundary_box)
        box.delete(boundary_box)
    
    if show_profile and max_volume > 0
        // Draw volume bars
        for i = 0 to resolution - 1
            level_volume = array.get(volume_bins, i)
            if level_volume > 0
                // Calculate price levels for this bin
                bin_bottom = bottom_price + (i * price_step)
                bin_top = bottom_price + ((i + 1) * price_step)
                
                // Calculate bar width based on volume
                volume_ratio = level_volume / max_volume
                bar_width = math.round(volume_ratio * profile_width)
                
                // Determine color based on volume level
                bar_color = color.new(color.gray, 90)
                if volume_ratio > 0.8
                    bar_color := hvn_color
                else if volume_ratio > 0.4
                    bar_color := mvn_color
                else if volume_ratio > 0.1
                    bar_color := lvn_color
                
                // Special color for POC
                if i == poc_level and show_poc
                    bar_color := poc_color
                
                // Create volume bar box
                if bar_width > 0
                    vol_box = box.new(
                        left=profile_start_bar, 
                        top=bin_top, 
                        right=profile_start_bar + bar_width, 
                        bottom=bin_bottom, 
                        bgcolor=bar_color, 
                        border_color=bar_color,
                        border_width=1
                    )
                    array.push(profile_boxes, vol_box)
        
        // Draw bounding box
        if show_bounds
            boundary_box := box.new(
                left=profile_start_bar - 2, 
                top=top_price, 
                right=profile_start_bar + profile_width + 2, 
                bottom=bottom_price, 
                bgcolor=color.new(color.white, 100), 
                border_color=bounds_color,
                border_width=1,
                border_style=line.style_dashed
            )

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// MAIN LOGIC
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Get previous day's high and low
[prev_day_high, prev_day_low] = get_prev_day_levels()

// Check if we have valid previous day data
var float stored_prev_high = na
var float stored_prev_low = na
var int profile_start_bar = na
var int profile_end_bar = na

// Detect new day and store previous day levels
is_new_day = ta.change(time("D")) != 0

if is_new_day and not na(prev_day_high) and not na(prev_day_low)
    stored_prev_high := prev_day_high
    stored_prev_low := prev_day_low
    profile_end_bar := bar_index - 1
    
    // Find the start of previous day (approximate)
    profile_start_bar := profile_end_bar
    for i = 1 to 2000 // Look back up to 2000 bars to find start of previous day
        if bar_index - i >= 0
            day_check = request.security(syminfo.tickerid, "D", time, barmerge.gaps_off, barmerge.lookahead_off)[i]
            if day_check != request.security(syminfo.tickerid, "D", time, barmerge.gaps_off, barmerge.lookahead_off)[i-1]
                profile_start_bar := bar_index - i
                break

// Calculate and draw volume profile on last bar
if barstate.islast and not na(stored_prev_high) and not na(stored_prev_low) and stored_prev_high > stored_prev_low
    // Calculate volume profile
    volume_bins = calculate_volume_profile(profile_start_bar, profile_end_bar, stored_prev_high, stored_prev_low, profile_resolution)
    
    // Find Point of Control
    [poc_level, max_volume] = find_poc(volume_bins)
    
    // Draw the profile
    draw_volume_profile(volume_bins, stored_prev_high, stored_prev_low, profile_resolution, max_volume, poc_level)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// PLOTTING
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Plot previous day high and low levels for reference
plot(stored_prev_high, "Previous Day High", color=color.new(color.red, 50), linewidth=1, style=plot.style_line)
plot(stored_prev_low, "Previous Day Low", color=color.new(color.green, 50), linewidth=1, style=plot.style_line)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// ALERTS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Alert when price reaches previous day high or low
alertcondition(close >= stored_prev_high, "Price reached Previous Day High", "Price has reached the Previous Day High level")
alertcondition(close <= stored_prev_low, "Price reached Previous Day Low", "Price has reached the Previous Day Low level")
